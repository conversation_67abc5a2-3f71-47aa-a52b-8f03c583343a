import os
import logging
import logging.config
from dataclasses import dataclass
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 统一配置环境变量默认值

@dataclass
class ConfigManager:
    openai_api_base: str = os.getenv("OPENAI_API_BASE")
    openai_api_key: str = os.getenv("OPENAI_API_KEY", "YOUR_API_KEY")
    openai_model: str = os.getenv("OPENAI_MODEL")
    openai_reasoning_effort: str = os.getenv("OPENAI_REASONING_EFFORT").lower()

    google_api_base: str = os.getenv("GOOGLE_API_BASE")
    google_api_key: str = os.getenv("GOOGLE_API_KEY", "YOUR_API_KEY")
    google_model: str = os.getenv("GOOGLE_MODEL")
    google_thinking_budget: int = int(os.getenv("GOOGLE_THINKING_BUDGET", -1))

    bailian_app_base: str = os.getenv("BAILIAN_APP_BASE")
    bailian_app_id: str = os.getenv("BAILIAN_APP_ID", "YOUR_APP_ID")
    bailian_api_base: str = os.getenv("BAILIAN_API_BASE")
    bailian_api_key: str = os.getenv("BAILIAN_API_KEY", "YOUR_API_KEY")
    bailian_model: str = os.getenv("BAILIAN_MODEL")
    qwen3_enable_thinking: bool = os.getenv("QWEN3_ENABLE_THINKING").lower() == 'true'

    coze_app_base: str = os.getenv("COZE_APP_BASE")
    coze_api_key: str = os.getenv("COZE_API_KEY", "YOUR_API_KEY")
    coze_workflow_id: str = os.getenv("COZE_WORKFLOW_ID", "YOUR_WORKFLOW_ID")
    
    volcano_api_base: str = os.getenv("VOLCANO_API_BASE")
    volcano_api_key: str = os.getenv("VOLCANO_API_KEY", "YOUR_API_KEY")
    volcano_model: str = os.getenv("VOLCANO_MODEL")
    doubao_seed_enable_thinking: bool = os.getenv("DOUBAO_SEED_ENABLE_THINKING").lower() == 'true'

    # 火山向量数据库配置
    volcano_knowledge_api_base: str = os.getenv("VOLCANO_KNOWLEDGE_API_BASE", "https://api-knowledgebase.mlp.cn-beijing.volces.com")
    volcano_knowledge_api_ak: str = os.getenv("VOLCANO_KNOWLEDGE_API_AK", "YOUR_API_AK")
    volcano_knowledge_api_sk: str = os.getenv("VOLCANO_KNOWLEDGE_API_SK", "YOUR_API_SK")
    volcano_knowledge_collection_name: str = os.getenv("VOLCANO_KNOWLEDGE_COLLECTION_NAME", "")
    volcano_knowledge_project: str = os.getenv("VOLCANO_KNOWLEDGE_PROJECT", "default")

    # Chroma向量数据库配置
    chroma_host: str = os.getenv("CHROMA_HOST", "localhost")
    chroma_port: int = int(os.getenv("CHROMA_PORT", "8000"))
    chroma_api_base: str = os.getenv("CHROMA_API_BASE", "http://localhost:8001/v1")
    chroma_api_key: str = os.getenv("CHROMA_API_KEY", "YOUR_API_KEY")
    chroma_collection_name: str = os.getenv("CHROMA_COLLECTION_NAME", "faq_knowledge")

    # 向量实现选择配置
    embedding_provider: str = os.getenv("EMBEDDING_PROVIDER", "volcano")  # volcano | chroma
    
    # FAQ数据配置
    faq_excel_path: str = os.getenv("FAQ_EXCEL_PATH", "./resources/faq_data/faq.xlsx")  # FAQ Excel文件路径
    rewrite_prompt_path: str = os.getenv("REWRITE_PROMPT_PATH", "./src/ai_app/agents/shared/prompts/common/rewrite_prompt.md")
    classify_prompt_path: str = os.getenv("CLASSIFY_PROMPT_PATH", "./src/ai_app/agents/shared/prompts/faq_filter/classify_prompt.md")
    
    # FAQ Recorder相关提示词配置
    faq_examine_system_prompt_path: str = os.getenv("FAQ_EXAMINE_SYSTEM_PROMPT_PATH", "./src/ai_app/agents/shared/prompts/faq_recorder/examine_system_prompt.md")
    faq_examine_user_prompt_path: str = os.getenv("FAQ_EXAMINE_USER_PROMPT_PATH", "./src/ai_app/agents/shared/prompts/faq_recorder/examine_user_prompt.md")
    faq_category_recommend_system_prompt_path: str = os.getenv("FAQ_CATEGORY_RECOMMEND_SYSTEM_PROMPT_PATH", "./src/ai_app/agents/shared/prompts/faq_recorder/category_recommend_system_prompt.md")
    faq_category_recommend_user_prompt_path: str = os.getenv("FAQ_CATEGORY_RECOMMEND_USER_PROMPT_PATH", "./src/ai_app/agents/shared/prompts/faq_recorder/category_recommend_user_prompt.md")
    faq_content_enhance_system_prompt_path: str = os.getenv("FAQ_CONTENT_ENHANCE_SYSTEM_PROMPT_PATH", "./src/ai_app/agents/shared/prompts/faq_recorder/content_enhance_system_prompt.md")
    faq_content_enhance_user_prompt_path: str = os.getenv("FAQ_CONTENT_ENHANCE_USER_PROMPT_PATH", "./src/ai_app/agents/shared/prompts/faq_recorder/content_enhance_user_prompt.md")

    rerank_api_base: str = os.getenv("RERANK_API_BASE")
    rerank_api_key: str = os.getenv("RERANK_API_KEY", "YOUR_API_KEY")
    rerank_model: str = os.getenv("RERANK_MODEL")
    
    fastapi_host: str = os.getenv("FASTAPI_HOST", "0.0.0.0")
    fastapi_port: int = int(os.getenv("FASTAPI_PORT", 8000))
    app_log_level: str = os.getenv("APP_LOG_LEVEL", "")
    
    # 日志文件配置
    app_log_dir: str = os.getenv("APP_LOG_DIR", "./logs")  # 日志目录，默认为当前运行目录下的 logs 文件夹
    app_log_rotate_when: str = os.getenv("APP_LOG_ROTATE_WHEN", "midnight")  # 日志轮转周期，默认按天轮转
    app_log_backup_count: int = int(os.getenv("APP_LOG_BACKUP_COUNT", 7))  # 日志文件保留天数，默认保留7天
    app_log_file_basename: str = os.getenv("APP_LOG_FILE_BASENAME", "app")  # 日志文件基础名，默认为 app

    def __post_init__(self):
        print("Configuration loaded:")
        for field, value in self.__dict__.items():
            if field.endswith("_api_key") or field.endswith("_api_ak") or field.endswith("_api_sk"):
                print(f"{field}: {value[:12] + '****' + value[-6:] if value else ''}")
            else:
                print(f"{field}: {value}")

    def get_model_config(self, model_platform: str):
        if model_platform == "volcano":
            return self.volcano_api_key, self.volcano_api_base, self.volcano_model
        elif model_platform == "bailian":
            return self.bailian_api_key, self.bailian_api_base, self.bailian_model
        elif model_platform == "openai":
            return self.openai_api_key, self.openai_api_base, self.openai_model
        elif model_platform == "google":
            return self.google_api_key, self.google_api_base, self.google_model
        else:
            raise ValueError(f"Invalid model platform: {model_platform}")
        
    def get_rerank_config(self):
        if self.rerank_api_key == "YOUR_API_KEY":
            return None, None, None
        return self.rerank_api_key, self.rerank_api_base, self.rerank_model

    def get_volcano_knowledge_config(self):
        """获取火山向量数据库配置。

        Returns:
            Tuple[str, str, str, str, str]: (api_base, api_ak, api_sk, collection_name, project)
        """
        if self.volcano_knowledge_api_ak == "YOUR_API_AK" or self.volcano_knowledge_api_sk == "YOUR_API_SK":
            return None, None, None, None, None
        return (
            self.volcano_knowledge_api_base,
            self.volcano_knowledge_api_ak,
            self.volcano_knowledge_api_sk,
            self.volcano_knowledge_collection_name,
            self.volcano_knowledge_project
        )

    def get_chroma_config(self):
        """获取Chroma向量数据库配置。

        Returns:
            Tuple[str, int, str, str, str]: (host, port, api_base, api_key, collection_name)
        """
        if self.chroma_api_key == "YOUR_API_KEY":
            return None, None, None, None, None
        return (
            self.chroma_host,
            self.chroma_port,
            self.chroma_api_base,
            self.chroma_api_key,
            self.chroma_collection_name
        )

    def get_embedding_provider(self):
        """获取向量实现提供者。

        Returns:
            str: 向量实现提供者 ("volcano" 或 "chroma")
        """
        return self.embedding_provider
        
    # 检查必要的环境变量是否已设置
    def check_bailian_vars(self):
        if self.bailian_api_key == "YOUR_API_KEY":
            raise ValueError("请在 .env 文件中设置 BAILIAN_API_KEY")

    def check_coze_vars(self):
        if self.coze_api_key == "YOUR_API_KEY":
            raise ValueError("请在 .env 文件中设置 COZE_API_KEY")

    def check_volcano_vars(self):
        if self.volcano_api_key == "YOUR_API_KEY":
            raise ValueError("请在 .env 文件中设置 VOLCANO_API_KEY")

    def check_openai_vars(self):
        if self.openai_api_key == "YOUR_API_KEY":
            raise ValueError("请在 .env 文件中设置 OPENAI_API_KEY")
            
    def check_google_vars(self):
        if self.google_api_key == "YOUR_API_KEY":
            raise ValueError("请在 .env 文件中设置 GOOGLE_API_KEY")

    def check_volcano_knowledge_vars(self):
        if self.volcano_knowledge_api_ak == "YOUR_API_AK" or self.volcano_knowledge_api_sk == "YOUR_API_SK":
            raise ValueError("请在 .env 文件中设置 VOLCANO_KNOWLEDGE_API_AK 和 VOLCANO_KNOWLEDGE_API_SK")
        if not self.volcano_knowledge_collection_name:
            raise ValueError("请在 .env 文件中设置 VOLCANO_KNOWLEDGE_COLLECTION_NAME")

    def check_chroma_vars(self):
        """验证Chroma相关环境变量。"""
        if self.chroma_api_key == "YOUR_API_KEY":
            raise ValueError("请在 .env 文件中设置 CHROMA_API_KEY")
        if not self.chroma_host:
            raise ValueError("请在 .env 文件中设置 CHROMA_HOST")
        if not self.chroma_collection_name:
            raise ValueError("请在 .env 文件中设置 CHROMA_COLLECTION_NAME")
        
        
config = ConfigManager()

# 日志配置函数
def get_logging_config(cli_verbose_flag: bool | None = None) -> dict:
    """
    生成日志配置字典。
    优先级: APP_LOG_LEVEL 环境变量 > cli_verbose_flag > 默认 "INFO"
    同时支持：
    - 控制台输出（StreamHandler）
    - 文件输出（TimedRotatingFileHandler，按天轮转）
    """
    env_log_level = config.app_log_level.upper()

    final_log_level = "INFO" # 默认

    if env_log_level in ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]:
        final_log_level = env_log_level
        logging.info(f"Log level set to '{final_log_level}' from APP_LOG_LEVEL environment variable.")
    elif cli_verbose_flag is True:
        final_log_level = "DEBUG"
        logging.info(f"Log level set to 'DEBUG' from command-line verbose flag.")
    else:
        logging.info(f"Log level set to default 'INFO'.")

    # 组装 handlers，根据是否配置了日志目录来决定是否启用文件日志
    handlers_default = ["default"]
    handlers_access = ["access"]

    # 如果配置了日志目录，则尝试创建目录并添加文件 handler
    log_dir = (config.app_log_dir or "").strip()
    if log_dir:
        try:
            os.makedirs(log_dir, exist_ok=True)
        except Exception as e:
            # 目录创建失败则降级仅输出到控制台
            logging.warning(f"Failed to create log dir '{log_dir}', fallback to console only. error={e}")
        else:
            # 启用文件日志
            handlers_default.append("file_default")
            handlers_access.append("file_access")

    log_file_basename = config.app_log_file_basename or "app"
    access_log_filename = os.path.join(log_dir, "access.log") if log_dir else None
    app_log_filename = os.path.join(log_dir, f"{log_file_basename}.log") if log_dir else None

    logging_config = {
        "version": 1,
        "disable_existing_loggers": False,
        "formatters": {
            "default": {
                "()": "uvicorn.logging.DefaultFormatter",
                "fmt": "%(levelprefix)s %(asctime)s - %(name)s - %(message)s",
                "datefmt": "%Y-%m-%d %H:%M:%S",
                "use_colors": None,
            },
            "access": {
                "()": "uvicorn.logging.AccessFormatter",
                "fmt": '%(levelprefix)s %(asctime)s - "%(client_addr)s" - "%(request_line)s" %(status_code)s',
                "datefmt": "%Y-%m-%d %H:%M:%S",
                "use_colors": None,
            },
            "file_default": {
                "()": "uvicorn.logging.DefaultFormatter",
                "fmt": "%(levelprefix)s %(asctime)s - %(name)s - %(message)s",
                "datefmt": "%Y-%m-%d %H:%M:%S",
                "use_colors": False,  # 文件日志明确禁用颜色
            },
            "file_access": {
                "()": "uvicorn.logging.AccessFormatter",
                "fmt": '%(levelprefix)s %(asctime)s - "%(client_addr)s" - "%(request_line)s" %(status_code)s',
                "datefmt": "%Y-%m-%d %H:%M:%S",
                "use_colors": False,  # 文件日志明确禁用颜色
            },
        },
        "handlers": {
            "default": {
                "formatter": "default",
                "class": "logging.StreamHandler",
                "stream": "ext://sys.stderr",
            },
            "access": {
                "formatter": "access",
                "class": "logging.StreamHandler",
                "stream": "ext://sys.stdout",
            },
        },
        "loggers": {
            "": {  # Root logger
                "handlers": handlers_default,
                "level": final_log_level,
                "propagate": False,
            },
            "uvicorn.error": {  # uvicorn 错误日志
                "level": "INFO",
                "handlers": handlers_default,
                "propagate": False,
            },
            "uvicorn.access": {  # uvicorn 访问日志
                "handlers": handlers_access,
                "level": "INFO",
                "propagate": False,
            },
        },
    }

    # 当有日志目录时，追加文件 handlers
    if log_dir:
        logging_config["handlers"]["file_default"] = {
            "formatter": "file_default",  # 使用无色formatter
            "class": "logging.handlers.TimedRotatingFileHandler",
            "filename": app_log_filename,
            "when": config.app_log_rotate_when or "midnight",
            "backupCount": int(config.app_log_backup_count or 7),
            "encoding": "utf-8",
            "utc": False,
        }
        logging_config["handlers"]["file_access"] = {
            "formatter": "file_access",  # 使用无色formatter
            "class": "logging.handlers.TimedRotatingFileHandler",
            "filename": access_log_filename,
            "when": config.app_log_rotate_when or "midnight",
            "backupCount": int(config.app_log_backup_count or 7),
            "encoding": "utf-8",
            "utc": False,
        }

    return logging_config

# 模块加载时应用一次日志配置 (主要服务于 Docker/直接 Uvicorn 启动场景)
logging.config.dictConfig(get_logging_config())
