# Chroma向量数据库集成方案

## 概述

本文档描述了将Chroma向量数据库集成到FAQ筛选代理中的完整方案。Chroma将作为火山向量数据库的替代方案，提供基于C/S架构的向量召回功能。

## 集成目标

1. **新增ChromaEmbeddingImpl实现**：基于Chroma HTTP客户端的向量检索实现
2. **保持接口兼容性**：遵循现有EmbeddingImpl接口规范
3. **支持多渠道数据**：通过metadata字段区分不同channel的数据
4. **配置化切换**：支持在火山向量数据库和Chroma之间灵活切换

## 技术架构

### 1. 数据库设计

#### Collection结构
- **单一Collection**：使用一个collection存储所有FAQ数据
- **Channel区分**：通过metadata中的`channel`字段区分不同渠道
- **数据结构**：
  ```json
  {
    "ids": ["faq_1_1_1", "faq_1_2_1", ...],
    "documents": ["问题内容+答案内容", ...],
    "metadatas": [
      {
        "channel": "common",
        "category_key_path": "1.1.1",
        "question": "原始问题",
        "answer": "原始答案",
        "category_desc": "分类描述路径"
      }
    ],
    "embeddings": [[0.1, 0.2, ...], ...]
  }
  ```

#### Metadata字段设计
- `channel`: 渠道标识（如"common", "ios", "android"等）
- `category_key_path`: FAQ分类路径（如"1.1.1"）
- `question`: 原始问题文本
- `answer`: 原始答案文本
- `category_desc`: 分类描述路径（用于显示）

### 2. 实现架构

```
ChromaEmbeddingImpl (新增)
├── 继承 EmbeddingImpl 抽象基类
├── 实现 search_knowledge() 方法
├── 支持 channel 筛选
└── 返回标准格式结果

FAQFilterAgent
├── 配置化选择向量实现
├── 火山向量数据库 (现有)
└── Chroma向量数据库 (新增)
```

## 实现方案

### 1. ChromaEmbeddingImpl类实现

**文件位置**: `backend_host/src/ai_app/agents/shared/llm_impl/chroma_impl.py`

**核心功能**:
- HTTP客户端连接管理
- Collection初始化和获取
- 基于metadata的channel筛选
- 向量相似度查询
- 结果格式转换

**关键方法**:
```python
class ChromaEmbeddingImpl(EmbeddingImpl):
    def __init__(self, host: str, port: int, collection_name: str, channel: str, embedding_function=None)
    def get_doc_name(self) -> str
    async def search_knowledge(self, query: str, top_n: int) -> Tuple[List[Dict[str, Any]], ChatModelUsage, Dict[str, Any]]
    def _ensure_collection_exists(self) -> Collection
    def _convert_results_to_faq_format(self, results: Dict) -> List[Dict[str, Any]]
```

### 2. 配置管理

**配置项添加** (`backend_host/src/ai_app/config.py`):
```python
# Chroma向量数据库配置
CHROMA_HOST = os.getenv("CHROMA_HOST", "localhost")
CHROMA_PORT = int(os.getenv("CHROMA_PORT", "8000"))
CHROMA_COLLECTION_NAME = os.getenv("CHROMA_COLLECTION_NAME", "faq_knowledge")
EMBEDDING_PROVIDER = os.getenv("EMBEDDING_PROVIDER", "volcano")  # volcano | chroma
```

**配置验证方法**:
```python
def check_chroma_vars():
    """验证Chroma相关环境变量"""
    
def get_chroma_config():
    """获取Chroma配置"""
    return CHROMA_HOST, CHROMA_PORT, CHROMA_COLLECTION_NAME
```

### 3. Agent集成修改

**文件**: `backend_host/src/ai_app/agents/faq_filter_agent/agent.py`

**修改点**:
1. 添加Chroma实现的导入
2. 在初始化时根据配置选择向量实现
3. 保持现有的向量召回逻辑不变

**代码示例**:
```python
# 根据配置选择向量实现
embedding_provider = config.get_embedding_provider()
if embedding_provider == "chroma":
    chroma_host, chroma_port, chroma_collection = config.get_chroma_config()
    embedding_impl = ChromaEmbeddingImpl(
        host=chroma_host,
        port=chroma_port,
        collection_name=chroma_collection,
        channel=self.channel_name
    )
elif embedding_provider == "volcano":
    # 现有的火山实现
    embedding_impl = VolcanoEmbeddingImpl(...)
```

### 4. 数据迁移工具

**工具脚本**: `backend_host/tools/migrate_faq_to_chroma.py`

**功能**:
- 从Excel FAQ数据源读取数据
- 转换为Chroma格式
- 批量写入Chroma collection
- 支持增量更新

## 部署配置

### 1. 环境变量配置

```bash
# Chroma服务配置
CHROMA_HOST=localhost
CHROMA_PORT=8000
CHROMA_COLLECTION_NAME=faq_knowledge

# 向量实现选择
EMBEDDING_PROVIDER=chroma  # volcano | chroma

# Embedding模型配置（用于Chroma）
EMBEDDING_API_BASE=http://your-embedding-service/v1
EMBEDDING_API_KEY=your-api-key
```

### 2. Chroma服务部署

**Docker部署**:
```yaml
# docker-compose.yml 新增服务
chroma:
  image: chromadb/chroma:latest
  ports:
    - "8000:8000"
  volumes:
    - ./chroma_data:/chroma/chroma
  environment:
    - CHROMA_SERVER_HOST=0.0.0.0
    - CHROMA_SERVER_HTTP_PORT=8000
```

### 3. 依赖管理

**添加依赖** (`backend_host/pyproject.toml`):
```toml
dependencies = [
    # ... 现有依赖
    "chromadb>=0.4.0",
]
```

## 测试方案

### 1. 单元测试

**测试文件**: `backend_host/tests/agents/shared/llm_impl/test_chroma_impl.py`

**测试覆盖**:
- ChromaEmbeddingImpl初始化
- search_knowledge方法功能
- channel筛选逻辑
- 错误处理机制

### 2. 集成测试

**测试文件**: `backend_host/tests/agents/faq_filter_agent/test_chroma_integration.py`

**测试场景**:
- FAQ筛选代理使用Chroma向量召回
- 多渠道数据查询
- 与现有火山实现的结果对比

### 3. 性能测试

**测试指标**:
- 查询响应时间
- 并发查询性能
- 内存使用情况

## 迁移策略

### 1. 渐进式迁移

1. **阶段1**: 实现ChromaEmbeddingImpl，保持火山实现为默认
2. **阶段2**: 在测试环境验证Chroma实现
3. **阶段3**: 生产环境配置化切换
4. **阶段4**: 完全迁移到Chroma（可选）

### 2. 数据同步

- 初始数据迁移：使用迁移工具一次性导入
- 增量同步：FAQ数据更新时同步更新Chroma
- 数据验证：定期验证两个向量库的数据一致性

## 监控和运维

### 1. 监控指标

- Chroma服务可用性
- 查询成功率和响应时间
- Collection数据量和更新频率

### 2. 日志记录

- 向量查询请求和响应
- 错误和异常情况
- 性能指标记录

### 3. 备份策略

- Chroma数据定期备份
- 配置文件版本控制
- 回滚方案准备

## 风险评估

### 1. 技术风险

- **兼容性风险**: Chroma版本升级可能影响API兼容性
- **性能风险**: 大规模数据查询性能需要验证
- **稳定性风险**: 新组件引入可能影响系统稳定性

### 2. 缓解措施

- 版本锁定和充分测试
- 性能基准测试和优化
- 灰度发布和快速回滚机制

## 总结

本方案提供了完整的Chroma向量数据库集成解决方案，包括：

1. **技术实现**: ChromaEmbeddingImpl类和相关配置
2. **数据设计**: 基于metadata的多渠道数据管理
3. **部署方案**: Docker化部署和环境配置
4. **测试策略**: 全面的测试覆盖
5. **迁移计划**: 渐进式迁移和风险控制

该方案保持了与现有系统的兼容性，支持灵活的配置切换，为FAQ筛选代理提供了更多的向量召回选择。
