#!/usr/bin/env python3
"""
Chroma HTTP 用法示例：知识库的创建、更新、召回与管理

说明：
- 本脚本演示如何通过 chromadb.HttpClient 连接到已启动的 Chroma HTTP Server，
  并进行集合（知识库）的创建/获取、写入（add/upsert/update）、查询（query）、
  获取（get）、删除（delete）、计数（count）、窥视（peek）等常用操作。
- 默认演示同步客户端（HttpClient）。如需异步，可参考官方 AsyncHttpClient 用法。

运行前提：
- 已经有可访问的 Chroma HTTP Server（默认假设 http://127.0.0.1:8000）。
- 环境安装 chromadb：uv add chromadb openai

用法示例：
- 运行完整演示流程：
  uv run python backend_host/tests/test_chroma_kb_http.py --mode demo
- 仅创建或获取集合：
  uv run python backend_host/tests/test_chroma_kb_http.py --mode init
- 添加示例数据：
  uv run python backend_host/tests/test_chroma_kb_http.py --mode add
- 使用 upsert（有则更、新则增）：
  uv run python backend_host/tests/test_chroma_kb_http.py --mode upsert
- 使用文本查询：
  uv run python backend_host/tests/test_chroma_kb_http.py --mode query_text --query "怎么修改密码"
"""

import argparse
import asyncio
import json
import logging
import logging.config
import os
from typing import Dict, List, Optional, Tuple

import chromadb
import chromadb.utils.embedding_functions as embedding_functions

# ----------------------------- 工具函数区 -----------------------------

# 新增：安全打印 JSON 的辅助函数，自动将 numpy.ndarray 等不可序列化对象转换

def _to_jsonable(obj):
    """将可能包含 numpy.ndarray 等对象的数据递归转换为可被 json.dumps 序列化的结构。
    - dict/list/tuple 递归处理
    - 对具有 tolist() 方法的对象（如 numpy.ndarray）优先调用 tolist()
    """
    if isinstance(obj, dict):
        return {k: _to_jsonable(v) for k, v in obj.items()}
    if isinstance(obj, (list, tuple)):
        return [_to_jsonable(x) for x in obj][:10]
    # 处理 numpy.ndarray 或类似对象
    if hasattr(obj, "tolist"):
        try:
            return [_to_jsonable(x) for x in obj.tolist()][:10]
        except Exception:
            pass
    return obj


def print_json(title: str, data) -> None:
    """安全地打印 JSON 数据。
    - 优先尝试 json.dumps 格式化
    - 若失败，退化为直接打印原对象，并附带错误信息
    """
    print(title)
    try:
        print(json.dumps(_to_jsonable(data), indent=2, ensure_ascii=False))
    except Exception as e:
        print(f"{data}\n(注意：JSON 序列化失败：{e})")


def simple_text_embedding(text: str, dim: int = 8) -> List[float]:
    """基于可重复的 hash 将文本映射为固定维度向量（仅用于示例，非真实语义向量）。
    参数：
    - text: 待编码文本
    - dim: 向量维度
    返回：长度为 dim 的浮点数组
    """
    # 注意：这是一个简易示例 embedding，用于在服务器无 embedding function 时演示。
    # 真实项目请用真实的向量模型生成 embeddings。
    base = abs(hash(text))
    vec = []
    for i in range(dim):
        vec.append(((base >> (i * 8)) & 0xFF) / 255.0)
    return vec


def batch_make_embeddings(texts: List[str], dim: int = 8) -> List[List[float]]:
    """批量生成“伪”向量，便于演示 add/upsert/query_embeddings 流程。"""
    return [simple_text_embedding(t, dim=dim) for t in texts]

# ----------------------------- 演示数据区 -----------------------------
SAMPLE_COLLECTION = "kb_demo_collection"
SAMPLE_DOCS = [
    "您好，请问如何修改密码？",
    "重置密码需要哪些步骤？",
    "如何进行账户充值？",
    "游戏出现卡顿该怎么办？",
]
SAMPLE_IDS = ["doc1", "doc2", "doc3", "doc4"]
SAMPLE_METAS = [
    {"category": "account", "lang": "zh", "channel": "common"},
    {"category": "account", "lang": "zh", "channel": "common"},
    {"category": "payment", "lang": "zh", "channel": "common"},
    {"category": "performance", "lang": "zh", "channel": "common"},
]

# ----------------------------- 操作函数区 -----------------------------

class ChromaClientManager:
    """Chroma 客户端单例管理器。"""
    _instance = None
    
    @classmethod
    def get_instance(cls):
        if cls._instance is None:
            cls._instance = ChromaClientManager()
        return cls._instance

    @classmethod
    def get_client(cls):
        client = cls.get_instance()._client
        if client is None:
            raise ValueError("Chroma client not initialized yet")
        return client
    
    @classmethod
    def get_collection(cls, collection_name: str = ''):
        client = cls.get_client()
        collection_name = collection_name or SAMPLE_COLLECTION
        coll = client.get_or_create_collection(
            name=collection_name,
            embedding_function=cls.get_instance()._embedding_function,
            configuration={
                "hnsw": {
                    "space": "cosine",
                }
            },
        )
        if coll is None:
            raise ValueError(f"Collection not found: {collection_name}")
        return coll
    
    def __init__(self):
        self._client = None
        self._embedding_function = None

    def initialize(self, host: str, port: int, api_base: str, api_key: str):
        self._client = chromadb.HttpClient(host=host, port=port)
        self._embedding_function = embedding_functions.OpenAIEmbeddingFunction(
            model_name="bge-m3",
            api_base=api_base,
            api_key=api_key
        )

def op_init(reset: bool = False) -> None:
    """初始化集合（知识库）。若已存在则直接返回。"""
    client = ChromaClientManager.get_client()
    if reset:
        try:
            client.delete_collection(SAMPLE_COLLECTION)
            print("已删除旧集合")
        except Exception:
            pass
    coll = ChromaClientManager.get_collection(SAMPLE_COLLECTION)
    print(f"集合已就绪：{coll.name}")


def op_add() -> None:
    """添加示例文档到集合。
    - use_fake_embeddings=True 时，使用脚本内置的“伪”向量，确保无服务端 embedding function 也可成功。
    """
    coll = ChromaClientManager.get_collection(SAMPLE_COLLECTION)
    coll.add(documents=SAMPLE_DOCS, metadatas=SAMPLE_METAS, ids=SAMPLE_IDS)
    print("已添加示例文档。当前条目数：", coll.count())


def op_upsert() -> None:
    """使用 upsert 对已有/新文档进行写入（有则更、新则增）。"""
    coll = ChromaClientManager.get_collection(SAMPLE_COLLECTION)
    # 假设我们更新 doc1 文档，同时新增一个 doc5
    new_docs = ["（更新）您好，请问如何修改密码？", "如何申请退款？"]
    new_ids = ["doc1", "doc5"]
    new_metas = [{"category": "account", "lang": "zh", "version": 2}, {"category": "payment", "lang": "zh"}]
    coll.upsert(documents=new_docs, metadatas=new_metas, ids=new_ids)
    print("已 upsert 两条数据。当前条目数：", coll.count())


def op_update_metadata_only() -> None:
    """仅更新某些条目的 metadata。"""
    coll = ChromaClientManager.get_collection(SAMPLE_COLLECTION)
    target_ids = ["doc3"]
    new_metas = [{"category": "payment", "lang": "zh", "channel": "common"}]
    coll.update(ids=target_ids, metadatas=new_metas)
    print("已更新 metadata：", target_ids)


def op_get_by_ids() -> None:
    """按 ID 获取若干条目，包含文档、向量与元数据。"""
    coll = ChromaClientManager.get_collection(SAMPLE_COLLECTION)
    data = coll.get(ids=["doc1", "doc3"], include=["metadatas", "documents", "embeddings"])
    print_json("GET 结果：", data)


def op_query_by_text(query: str, n_results: int = 3, where: Optional[Dict] = None) -> None:
    """基于 query_texts 的相似度查询（需集合配置了 embedding function）。"""
    coll = ChromaClientManager.get_collection(SAMPLE_COLLECTION)
    results = coll.query(query_texts=[query], n_results=n_results, where=where, include=["metadatas", "documents", "distances"])
    print_json("文本查询结果：", results)


def op_peek(limit: int = 5) -> None:
    """窥视集合前若干条数据。"""
    coll = ChromaClientManager.get_collection(SAMPLE_COLLECTION)
    data = coll.peek(limit=limit)
    print_json("PEEK 结果：", data)

def op_count() -> None:
    """统计集合内条目数量。"""
    coll = ChromaClientManager.get_collection(SAMPLE_COLLECTION)
    print("COUNT：", coll.count())


def op_delete() -> None:
    """演示删除：按 ID 删除与按 where 条件删除。"""
    coll = ChromaClientManager.get_collection(SAMPLE_COLLECTION)
    # 按 ID 删除一个条目
    deleted_ids = coll.delete(ids=["doc5"])  # 可能不存在也不会报错
    print("按 ID 删除：", deleted_ids)
    # 按 where 条件删除（删除 category=performance 的条目）
    deleted_ids2 = coll.delete(where={"category": {"$eq": "performance"}})
    print("按 where 删除：", deleted_ids2)
    print("删除后 COUNT：", coll.count())


def op_full_demo() -> None:
    """运行从初始化到查询、更新、删除的完整演示流程，并打印每一步的结果。"""
    op_init(True)
    op_add()
    op_peek(limit=3)
    op_count()
    op_query_by_text(query="怎么修改密码", n_results=3)
    op_update_metadata_only()
    op_get_by_ids()
    op_upsert()
    op_delete()


# ----------------------------- CLI 入口 -----------------------------

async def main():
    """命令行入口：根据 --mode 参数执行不同演示操作。"""
    parser = argparse.ArgumentParser(description="Chroma HTTP 知识库示例与测试")
    parser.add_argument("--host", default=os.environ.get("CHROMA_HOST", "127.0.0.1"), help="Chroma Server 主机名")
    parser.add_argument("--port", type=int, default=int(os.environ.get("CHROMA_PORT", "8000")), help="Chroma Server 端口")
    parser.add_argument("--api-base", default=os.environ.get("EMBEDDING_API_BASE"), help="Embedding Model API 基础路径")
    parser.add_argument("--api-key", default=os.environ.get("EMBEDDING_API_KEY"), help="Embedding Model API 密钥")
    parser.add_argument("--mode", choices=[
        "async", "demo", "init", "add", "upsert", "update_meta", "get", "query_text", "peek", "count", "delete"
    ], default="demo", help="要运行的示例模式")
    parser.add_argument("--reset", action="store_true", help="是否重置集合（删除所有数据）")
    parser.add_argument("--query", default="怎么修改密码", help="查询文本（用于 query_* 模式）")
    parser.add_argument("--n-results", type=int, default=3, help="召回数量")
    parser.add_argument("--where-json", default=None, help="where 过滤条件（JSON 字符串）")
    args = parser.parse_args()

    if args.mode == "async":
        client = await chromadb.AsyncHttpClient(host=args.host, port=args.port)
        coll = await client.get_or_create_collection(
            name=SAMPLE_COLLECTION,
            embedding_function=embedding_functions.OpenAIEmbeddingFunction(
                model_name="bge-m3",
                api_base=args.api_base,
                api_key=args.api_key
            ),
            configuration={
                "hnsw": {
                    "space": "cosine",
                }
            },
        )
        data = await coll.peek(5) 
        print_json("PEEK 结果：", data)
        return

    ChromaClientManager.get_instance().initialize(
        host=args.host,
        port=args.port,
        api_base=args.api_base,
        api_key=args.api_key,
    )
    print(f"连接到 Chroma Server: http://{args.host}:{args.port}")
    print(f"Embedding Model API 基础路径: {args.api_base}")
    print(f"Embedding Model API 密钥: {args.api_key[:10]}...{args.api_key[-6:]}")

    where = None
    if args.where_json:
        try:
            where = json.loads(args.where_json)
        except json.JSONDecodeError:
            print("where-json 不是合法的 JSON，将忽略。")

    if args.mode == "demo":
        op_full_demo()
    elif args.mode == "init":
        op_init(reset=args.reset)
    elif args.mode == "add":
        op_add()
    elif args.mode == "upsert":
        op_upsert()
    elif args.mode == "update_meta":
        op_update_metadata_only()
    elif args.mode == "get":
        op_get_by_ids()
    elif args.mode == "query_text":
        op_query_by_text(args.query, n_results=args.n_results, where=where)
    elif args.mode == "peek":
        op_peek()
    elif args.mode == "count":
        op_count()
    elif args.mode == "delete":
        op_delete()


if __name__ == "__main__":
    # 采用项目已有的日志配置风格（如需），这里保持简单输出
    asyncio.run(main())