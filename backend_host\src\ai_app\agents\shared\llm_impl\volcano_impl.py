import httpx
import json
from typing import List, Dict, Any, Tuple, Optional

from ai_app.config import config
from ai_app.models.chat import ChatModelUsage
from ai_app.shared.exceptions import LLMAPIError, LLMResponseError, EmbeddingCollectionNotFoundError
from ai_app.agents.shared.llm_impl.base_llm_impl import BaseLLMImpl, DEFAULT_TIMEOUT # Updated import
from ai_app.agents.shared.llm_impl.embedding_impl import EmbeddingImpl
from ai_app.shared.request_tracing import get_traced_logger

# 火山引擎签名相关导入
from volcengine.auth.SignerV4 import SignerV4
from volcengine.base.Request import Request
from volcengine.Credentials import Credentials

logger = get_traced_logger(__name__)

class VolcanoLLMImpl(BaseLLMImpl): # Inherits from BaseLLMImpl
    """与火山方舟大模型服务平台交互的具体实现。"""

    def __init__(self, api_key: str, api_base: str, model_id: str):
        """初始化火山方舟客户端。

        Args:
            api_key: 火山方舟 API 密钥 (AK)。
            api_base: 火山方舟 API 基础 URL (e.g., 'https://ark.cn-beijing.volces.com/api/v3')。
            model_id: 模型 ID。
        """
        # 调用基类构造函数进行通用初始化
        super().__init__(api_key, api_base, model_id)
        # httpx.AsyncClient 实例现在在 chat_completion 方法内部创建和管理，以确保上下文正确关闭

    # 实现基类的抽象方法
    async def chat_completion(
        self,
        messages: List[Dict[str, str]],
        timeout: float = DEFAULT_TIMEOUT,
        temperature: Optional[float] = None,
        top_p: Optional[float] = None,
        max_tokens: Optional[int] = None,
        response_format: Optional[Dict[str, str]] = None # For JSON mode
    ) -> Tuple[str, ChatModelUsage, Dict[str, Any]]:
        """调用火山方舟 /chat/completions API。"""
        api_url = f"{self.api_base}/chat/completions"
        request_body = {
            "model": self.model_id,
            "messages": messages,
            "stream": False, # 确保 stream 为 False 以匹配非流式 chat_completion 接口
        }

        # 是否开启thinking
        if config.doubao_seed_enable_thinking:
            request_body["thinking"] = {"type": "enabled"}
        else:
            request_body["thinking"] = {"type": "disabled"}

        # Add optional parameters if provided
        if temperature is not None:
            request_body["temperature"] = temperature
        if top_p is not None:
            request_body["top_p"] = top_p
        if max_tokens is not None:
            # 火山 API 可能叫 max_tokens 或 max_new_tokens，需确认文档
            # 假设是 max_tokens
            request_body["max_tokens"] = max_tokens
        if response_format is not None:
             request_body["response_format"] = response_format # 假设火山支持 OpenAI 风格的 response_format

        logger.debug(f"Calling Volcano API: {api_url} with model {self.model_id}")
        logger.debug(f"Request Body: {json.dumps(request_body, ensure_ascii=False, indent=2)}")

        async with httpx.AsyncClient(timeout=timeout) as client:
            try:
                response = await client.post(
                    api_url,
                    headers=self.headers,
                    json=request_body
                )
                response.raise_for_status() # Check for 4xx/5xx errors
            except httpx.TimeoutException as e:
                logger.error(f"Volcano API request timed out to {api_url}: {e}")
                raise LLMAPIError(f"Request timed out after {timeout}s: {e}") from e
            except httpx.RequestError as e:
                logger.error(f"Volcano API request error to {api_url}: {e}")
                raise LLMAPIError(f"Request failed: {e}") from e
            except httpx.HTTPStatusError as e:
                error_detail = e.response.text
                try:
                    error_json = e.response.json()
                    error_detail = error_json.get('error', {}).get('message', error_detail)
                except json.JSONDecodeError:
                    pass
                logger.error(f"Volcano API returned error status {e.response.status_code} from {api_url}: {error_detail}")
                raise LLMAPIError(f"API returned status {e.response.status_code}: {error_detail}") from e

        try:
            response_data = response.json()
            logger.debug(f"Raw Volcano API response: {json.dumps(response_data, indent=2, ensure_ascii=False)}")

            if 'error' in response_data and response_data['error']:
                error_info = response_data['error']
                error_message = error_info.get('message', json.dumps(error_info))
                logger.error(f"Volcano API returned error in response body: {error_message}")
                raise LLMAPIError(f"API returned error: {error_message}")

            # 确保按预期结构提取数据
            if not response_data.get('choices') or not response_data['choices'][0].get('message') or 'content' not in response_data['choices'][0]['message']:
                 logger.error(f"Unexpected response structure from Volcano API. Missing 'choices[0].message.content'. Response: {response_data}")
                 raise LLMResponseError("Unexpected API response structure: Missing content.")

            content = response_data['choices'][0]['message']['content']

            if not response_data.get('usage') or 'prompt_tokens' not in response_data['usage'] or 'completion_tokens' not in response_data['usage']:
                 logger.warning(f"Usage information missing or incomplete in Volcano API response. Response: {response_data}")
                 # 可以选择返回默认值或部分信息
                 usage = ChatModelUsage(
                     model_id=response_data.get('model', self.model_id), # 尝试获取模型 ID
                     input_tokens=response_data.get('usage', {}).get('prompt_tokens', 0),
                     output_tokens=response_data.get('usage', {}).get('completion_tokens', 0)
                 )
            else:
                 usage = ChatModelUsage(
                    model_id=response_data['model'],
                    input_tokens=response_data['usage']['prompt_tokens'],
                    output_tokens=response_data['usage']['completion_tokens']
                 )

            # 使用基类的方法移除可能的 JSON 包裹
            cleaned_content = self.clean_content(content)

            return cleaned_content, usage, response_data

        except json.JSONDecodeError as e:
            logger.error(f"Failed to decode JSON response from Volcano API: {response.text}")
            raise LLMResponseError(f"Failed to decode API JSON response: {e}") from e
        except (KeyError, IndexError, TypeError) as e:
             logger.error(f"Failed to parse expected data from Volcano API response. Response: {response_data}. Error: {e}", exc_info=True)
             raise LLMResponseError(f"Unexpected API response structure: {e}") from e 

class VolcanoEmbeddingImpl(EmbeddingImpl):
    """与火山引擎向量数据库交互的具体实现。"""

    def __init__(self, api_base: str, api_ak: str, api_sk: str, project: str = "default", collection_name: str = "default", channel: str = 'common'):
        """初始化火山向量数据库客户端。

        Args:
            api_base: 火山引擎 API 基础 URL。
            api_ak: 火山引擎 API 密钥 (AK)。
            api_sk: 火山引擎 API 密钥 (SK)。
            project: 知识库所属项目，默认为 "default"。
            collection_name: 知识库名称，默认为 "default"。
            channel: 知识库渠道，默认为 "common"。
        """

        self.api_base = api_base.rstrip('/')
        self.api_path = '/api/knowledge/collection/search_knowledge'
        self.api_ak = api_ak
        self.api_sk = api_sk
        self.project = project
        self.collection_name = collection_name
        self.channel = channel

    def get_doc_name(self) -> str:
        return f"{self.project}.{self.collection_name}.{self.channel}"

    def prepare_request(self, data: Dict[str, Any], timeout: int) -> Request:
        """根据火山引擎官方文档实现的签名生成函数。

        Args:
            data: 请求体，适用于 POST、PUT 请求
            timeout: 请求超时时间 (秒)。

        Returns:
            Request: 已签名的请求对象
        """

        host = self.api_base.replace("https://", "").replace("http://", "")

        # 创建请求对象
        r = Request()
        r.set_shema("https")
        r.set_method("POST")
        r.set_connection_timeout(timeout)
        r.set_socket_timeout(timeout)


        # 设置请求头
        mheaders = {
            "Accept": "application/json",
            "Content-Type": "application/json",
            "Host": host,
        }
        r.set_headers(mheaders)

        # 设置主机和路径
        r.set_host(host)
        r.set_path(self.api_path)

        # 设置请求体
        if data is not None:
            r.set_body(json.dumps(data))

        # 生成签名
        credentials = Credentials(self.api_ak, self.api_sk, "air", "cn-north-1")
        SignerV4.sign(r, credentials)

        return r

    async def search_knowledge(self,
        query: str,
        top_n: int,
    ) -> Tuple[List[Dict[str, Any]], ChatModelUsage, Dict[str, Any]]:
        """调用火山向量数据库 search_knowledge API 进行知识检索。

        Args:
            query: 检索文本，最大可输入长度为 8000。
            top_n: 检索结果数量，范围 [1, 200]。

        Returns:
            Tuple[List[Dict[str, Any]], ChatModelUsage, Dict[str, Any]]: 包含检索结果、token使用量和原始响应。

        Raises:
            LLMAPIError: 如果 API 调用失败。
            LLMResponseError: 如果 API 响应格式不正确。
            EmbeddingCollectionNotFoundError: 如果知识库不存在。
        """
        # 构建请求体
        request_body = {
            "name": self.collection_name,
            "project": self.project,
            "query": query,
            "limit": min(max(top_n, 1), 200),  # 确保在有效范围内
            "query_param": {
                "doc_filter": {
                    "op": "must",
                    "field": "doc_id",
                    "conds": [self.channel]
                }
            },
            "dense_weight": 0.5,  # 混合检索权重，写死为0.5
            "pre_processing": {
                "need_instruction": False,
                "rewrite": False,
                "return_token_usage": True
            },
            "post_processing": {
                "rerank_switch": False,  # 不启用重排序
                "chunk_group": False,  # 结构化文档不需要文本聚合
            }
        }

        # 使用prepare_request生成签名请求
        signed_request = self.prepare_request(
            data=request_body,
            timeout=DEFAULT_TIMEOUT
        )

        # 构建完整URL
        api_url = f"{self.api_base}{self.api_path}"

        logger.debug(f"Calling Volcano Knowledge API: {api_url}")
        logger.debug(f"Request Body: {json.dumps(request_body, ensure_ascii=False, indent=2)}")

        # 使用httpx.AsyncClient发送异步请求
        async with httpx.AsyncClient(timeout=DEFAULT_TIMEOUT) as client:
            try:
                response = await client.post(
                    api_url,
                    headers=signed_request.headers,
                    content=signed_request.body,
                )
                response.raise_for_status()
            except httpx.TimeoutException as e:
                logger.error(f"Volcano Knowledge API request timed out to {api_url}: {e}")
                raise LLMAPIError(f"Request timed out after {DEFAULT_TIMEOUT}s: {e}") from e
            except httpx.RequestError as e:
                logger.error(f"Volcano Knowledge API request error to {api_url}: {e}")
                raise LLMAPIError(f"Request failed: {e}") from e
            except httpx.HTTPStatusError as e:
                error_detail = e.response.text
                try:
                    error_json = e.response.json()
                    error_detail = error_json.get('message', error_detail)
                    error_code = error_json.get('code')
                except json.JSONDecodeError:
                    pass
                if error_code == 1000034:
                    # 1000034错误码表示集合不存在，此时返回上层特殊异常，以便重试
                    logger.debug(f"Volcano Knowledge API returned Collection({self.collection_name}) not found: {error_json}")
                    raise EmbeddingCollectionNotFoundError(f"Collection not found: {self.collection_name}") from e
                logger.error(f"Volcano Knowledge API returned error status {e.response.status_code} from {api_url}: {error_code} {error_detail}")
                raise LLMAPIError(f"API returned status {e.response.status_code}: {error_code} {error_detail}") from e

        try:
            response_data = response.json()
            logger.debug(f"Raw Volcano Knowledge API response: {json.dumps(response_data, indent=2, ensure_ascii=False)}")

            # 检查响应状态码
            if response_data.get('code') != 0:
                error_message = response_data.get('message', 'Unknown error')
                logger.error(f"Volcano Knowledge API returned error code {response_data.get('code')}: {error_message}")
                raise LLMAPIError(f"API returned error: {error_message}")

            # 提取数据
            data = response_data.get('data', {})
            result_list = data.get('result_list', [])

            return_list = []

            for result in result_list:
                return_list.append({
                    'score': result['score'],
                    'content': result['table_chunk_fields'],
                })

            # 构建 ChatModelUsage
            token_usage_data = data.get('token_usage', {})
            embedding_usage = token_usage_data.get('embedding_token_usage', {})

            usage = ChatModelUsage(
                model_id="volcano-embedding",  # 火山向量数据库的模型标识
                input_tokens=embedding_usage.get('prompt_tokens', 0),
                output_tokens=embedding_usage.get('completion_tokens', 0)
            )

            logger.debug(f"Volcano Knowledge search completed. Retrieved {len(result_list)} results for query: {query[:100]}...")

            return return_list, usage, response_data

        except json.JSONDecodeError as e:
            logger.error(f"Failed to decode JSON response from Volcano Knowledge API: {response.text}")
            raise LLMResponseError(f"Failed to decode API JSON response: {e}") from e
        except (KeyError, TypeError) as e:
            logger.error(f"Failed to parse expected data from Volcano Knowledge API response. Response: {response_data}. Error: {e}", exc_info=True)
            raise LLMResponseError(f"Unexpected API response structure: {e}") from e