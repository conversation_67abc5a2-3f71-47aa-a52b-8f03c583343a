import json
from typing import Dict, Any

# Import the necessary classes from other modules
# FAQDataParser现在通过FAQRepoManager创建，不需要直接导入
from ai_app.agents.faq_filter_agent.llm_clients import QueryRewriteClient, FAQClassifierClient, FAQRerankClient, FAQRetrieveClient
from ai_app.models.chat import ChatRequest, ChatModelUsages # Adjusted import path
from ai_app.models.chat_to_faq_filter import ChatToFaqFilterCandidate, ChatToFaqFilterResponse
from ai_app.shared.exceptions import ConfigurationError, PromptLoadError, EmbeddingCollectionNotFoundError # Import custom exceptions
# Import the specific LLM implementation
from ai_app.agents.shared.llm_impl.base_llm_impl import BaseLLMImpl
from ai_app.agents.shared.llm_impl.volcano_impl import VolcanoLLMImpl, VolcanoEmbeddingImpl
from ai_app.agents.shared.llm_impl.chroma_impl import ChromaEmbeddingImpl
from ai_app.agents.shared.llm_impl.bailian_impl import Bai<PERSON><PERSON><PERSON>mpl
from ai_app.agents.shared.llm_impl.google_impl import Google<PERSON><PERSON>mpl
from ai_app.agents.shared.llm_impl.rerank_impl import RerankImpl
from ai_app.services.faq_management.faq_repo_manager import get_faq_repo_manager
from ai_app.shared.quick_matcher import QuickMatcher

from ai_app.config import config
from ai_app.shared.request_tracing import get_traced_logger

# Setup logger for this module
logger = get_traced_logger(__name__)

class FAQFilterAgent:
    """AI Agent 的主入口和协调器。"""

    def __init__(self, context_params: Dict[str, Any] = None, model_platform: str = None):
        """初始化 Agent，从 config.py 加载配置并创建依赖项。"""

        try:
            self.channel_name = None
            if context_params is not None and context_params.get('channel_name') is not None:
                self.channel_name = context_params['channel_name']

            # 获取FAQ仓库管理器并创建parser
            try:
                faq_repo_manager = get_faq_repo_manager()
                self.faq_parser = faq_repo_manager.create_parser(channel=self.channel_name, allow_fallback=True)
                # 修正channel
                self.channel_name = self.faq_parser.channel
                logger.debug(f"Successfully created FAQ parser for channel: {self.channel_name}")
            except Exception as e:
                logger.error(f"Failed to create FAQ parser from repository: {e}")
                raise ConfigurationError(f"FAQ解析器创建失败: {e}") from e

            # Load prompts
            try:
                with open(config.rewrite_prompt_path, 'r', encoding='utf-8') as f:
                    rewrite_prompt = f.read()
                with open(config.classify_prompt_path, 'r', encoding='utf-8') as f:
                    classify_prompt = f.read()
                logger.debug("Successfully loaded prompt files.")
            except FileNotFoundError as e:
                logger.error(f"Prompt file not found: {e}")
                raise PromptLoadError(f"Required prompt file not found: {e}") from e
            except IOError as e:
                 logger.error(f"Error reading prompt file: {e}")
                 raise PromptLoadError(f"Could not read prompt file: {e}") from e

            # Initialize LLM Implementation for rewrite/classify based on config
            api_key, api_base, model_id = config.get_model_config(model_platform)
            if model_platform == "volcano":
                llm_impl = VolcanoLLMImpl(api_key, api_base, model_id)
            elif model_platform == "bailian":
                llm_impl = BailianLLMImpl(api_key, api_base, model_id)
            elif model_platform == "google":
                llm_impl = GoogleLLMImpl(api_key, api_base, model_id)
            else:
                llm_impl = BaseLLMImpl(api_key, api_base, model_id)
            
            rerank_api_key, rerank_api_base, rerank_model_id = config.get_rerank_config()
                
            # Initialize other components
            self.rewrite_client = QueryRewriteClient(
                llm_client=llm_impl, 
                prompt_template=rewrite_prompt
            )
            self.classifier_client = FAQClassifierClient(
                llm_client=llm_impl, 
                prompt_template=classify_prompt
            )

            if rerank_api_key and rerank_api_base and rerank_model_id:
                self.rerank_client = FAQRerankClient(llm_client=RerankImpl(
                    api_key=rerank_api_key,
                    api_base=rerank_api_base,
                    model_id=rerank_model_id
                ))
            else:
                self.rerank_client = None

            # Initialize retrieve client for fallback retrieval
            embedding_provider = config.get_embedding_provider()
            if embedding_provider == "chroma":
                # 使用Chroma向量数据库
                chroma_host, chroma_port, chroma_api_base, chroma_api_key, chroma_collection_name = config.get_chroma_config()
                if chroma_host and chroma_api_key and chroma_collection_name:
                    embedding_impl = ChromaEmbeddingImpl(
                        host=chroma_host,
                        port=chroma_port,
                        api_base=chroma_api_base,
                        api_key=chroma_api_key,
                        collection_name=chroma_collection_name,
                        channel=self.channel_name
                    )
                    self.retrieve_client = FAQRetrieveClient(embedding_impl)
            elif embedding_provider == "volcano":
                # 使用火山向量数据库（原有逻辑）
                volcano_knowledge_api_base, volcano_knowledge_api_ak, volcano_knowledge_api_sk, volcano_knowledge_collection_name, volcano_knowledge_project = config.get_volcano_knowledge_config()
                if volcano_knowledge_api_base and volcano_knowledge_api_ak and volcano_knowledge_api_sk:
                    embedding_impl = VolcanoEmbeddingImpl(
                        api_base=volcano_knowledge_api_base,
                        api_ak=volcano_knowledge_api_ak,
                        api_sk=volcano_knowledge_api_sk,
                        project=volcano_knowledge_project,
                        collection_name=volcano_knowledge_collection_name,
                        channel=self.channel_name
                    )
                    self.retrieve_client = FAQRetrieveClient(embedding_impl)
            else:
                self.retrieve_client = None
                logger.warning(f"Unknown embedding provider: {embedding_provider}, retrieve client disabled")
                
        except KeyError as e:
            logger.error(f"Missing configuration key: {e}")
            raise ConfigurationError(f"Missing required configuration key: {e}") from e
        except (ConfigurationError, PromptLoadError) as e:
            # Re-raise exceptions related to config/prompt loading
            logger.error(f"Failed to initialize FAQFilterAgent due to error: {e}")
            raise
        except Exception as e:
            # Catch any other unexpected errors during initialization
            logger.exception(f"Unexpected error during FAQFilterAgent initialization: {e}")
            raise ConfigurationError(f"An unexpected error occurred during agent initialization: {e}") from e

    async def process_user_request(self, chat_request: ChatRequest) -> ChatToFaqFilterResponse:
        """处理用户请求的完整流程。

        Args:
            chat_request: 包含对话历史和上下文信息的请求对象。

        Returns:
            一个包含响应文本和会话 ID 的响应对象。
        """
        logger.info(f"--- FAQFilterAgent: process_user_request called (Session ID: {chat_request.session_id}) ---")

        # Initialize usages list
        model_usages_list = [] # MODIFIED: To collect all usages

        # Extract conversation history and context from ChatRequest
        # Convert ChatInputMessage objects to the dict format expected by rewrite_client
        conversation_dicts = [{"role": msg.role, "content": msg.content} for msg in chat_request.conversation]
        context = chat_request.context_params or {} # Use context_params if available

        # Prepare input data for the rewrite client
        rewrite_input_data = {
            "conversation": conversation_dicts,
            "context": context
        }

        # 1. 查询重写 (Query Rewrite)
        try:
            rewritten_query, rewritten_usage = await self.rewrite_client.rewrite_query(input_data=rewrite_input_data)
            if rewritten_usage: # MODIFIED: Add usage if available
                model_usages_list.append(rewritten_usage)

            if not rewritten_query:
                logger.error("Failed to rewrite query: LLM did not return expected 'query_rewrite' field.")
                return ChatToFaqFilterResponse( # Typo in original: ChatToFqFilterResponse -> ChatToFaqFilterResponse
                    response_code=500,
                    response_text="Failed to understand the query context.",
                    session_id=chat_request.session_id
                )
            logger.info(f"Rewritten Query: {rewritten_query}")
        except Exception as e: 
             logger.exception(f"Error during query rewrite: {e}")
             return ChatToFaqFilterResponse(
                 response_code=500,
                 response_text="An error occurred while processing your query context.",
                 session_id=chat_request.session_id
             )

        # 2. 获取 FAQ 目录结构
        try:
            faq_structure_md = self.faq_parser.get_category_structure_markdown()
            if not faq_structure_md:
                 logger.error("Failed to get FAQ structure: Parser returned empty structure.")
                 return ChatToFaqFilterResponse(
                     response_code=500,
                     response_text="Failed to load internal knowledge base.",
                     session_id=chat_request.session_id
                 )
        except Exception as e:
            logger.exception(f"Error getting FAQ structure: {e}")
            return ChatToFaqFilterResponse(
                 response_code=500,
                 response_text="An error occurred accessing internal knowledge base.",
                 session_id=chat_request.session_id
            )

        # 3. 问题分类 (Classification)
        try:
            classification_data, classification_usage, classification_thinking = await self.classifier_client.classify_query(rewritten_query, faq_structure_md)
            if classification_usage: # MODIFIED: Add usage if available
                model_usages_list.append(classification_usage)

            if not classification_data or not all('category_key_path' in item for item in classification_data):
                logger.error("Failed to classify query: LLM did not return expected 'category_key_path' field.")
                return ChatToFaqFilterResponse(
                    response_code=500,
                    response_text="Failed to classify the query. Wrong-format response from LLM.",
                    session_id=chat_request.session_id
                )
            

            result_list = []
            for index, item in enumerate(classification_data):
                classification_answers = self.faq_parser.get_answers_by_key_path(item['category_key_path'])
                for classification_answer in classification_answers:
                    result = {
                        'key_path': classification_answer['key_path'],
                        'reason': f'意图分类：{item["category_key_path"]}'
                    }
                    logger.info(f"Classification({index}) Path: {result['key_path']} from {item['category_key_path']}")
                    result_list.append(result)
        except Exception as e:
            logger.exception(f"Error during query classification: {e}")
            return ChatToFaqFilterResponse(
                 response_code=500,
                 response_text="An error occurred while classifying your query.",
                 session_id=chat_request.session_id
             )
        
        # 3.1 问题召回
        if self.retrieve_client:
            try:
                retrieve_results = None
                retrieve_usage = None
                retrieve_count = 5
                logger.info(f"Starting retrieval. count: {retrieve_count}")

                # 首先尝试带channel后缀的collection
                retrieve_results, retrieve_usage = await self.retrieve_client.retrieve_from_query(
                    query=rewritten_query,
                    top_n=retrieve_count
                )

                if retrieve_usage:
                    model_usages_list.append(retrieve_usage)

                # 将召回结果添加到result_list中，仿照步骤3的逻辑
                for index, retrieve_item in enumerate(retrieve_results):
                    # 已经被分类选中的答案跳过
                    if any(retrieve_item['key_path'] == result['key_path'] for result in result_list):
                        logger.debug(f"Retrieval knowledge({index}). Path: {retrieve_item['key_path']} skipped because already in classification results.")
                        continue

                    # 加入新增的召回答案
                    result = {
                        'key_path': retrieve_item['key_path'],
                        'reason': f"向量召回：{retrieve_item['score']}"
                    }
                    logger.info(f"Retrieval knowledge({index}). Path: {retrieve_item['key_path']}, score: {retrieve_item['score']}")
                    result_list.append(result)

            except Exception as e:
                logger.exception(f"Error during retrieval: {e}")
                return ChatToFaqFilterResponse(
                    response_code=500,
                    response_text="An error occurred while retrieving supplementary information.",
                    session_id=chat_request.session_id
                )
        else:
            logger.info("No retrieve client available, skipping retrieval.")

        # 4. 答案检索
        for result in result_list: # result_list now contains dicts like {'key_path': ..., 'reason': ...}
            try:
                answer_data = self.faq_parser.get_answer_by_key_path(result['key_path'])
                if answer_data:
                    result['final_answer'] = answer_data['answer']
                    result['question_example'] = answer_data['question_example']
                    result['desc_path'] = answer_data['desc_path']
                    result['score'] = 0.0 # 0.0 for retrieved answers
                else:
                    result['score'] = -1.0 # -1.0 for no answer
            except Exception as e:
                logger.exception(f"Error during answer retrieval for path '{result['category_key_path']}': {e}")
                return ChatToFaqFilterResponse(
                    response_code=500,
                    response_text="An error occurred while retrieving the answer.",
                    session_id=chat_request.session_id
                )

        # 5. 答案重排 (Reranking) - MODIFIED SECTION
        if self.rerank_client: # Check if rerank_client was initialized successfully
            try:
                # rerank_retrieve_results expects a list of dicts, and will add/update 'score' in them
                # It returns the sorted list and usage.
                result_list, rerank_usage = await self.rerank_client.rerank_retrieve_results(rewritten_query, result_list)
                
                if rerank_usage:
                    model_usages_list.append(rerank_usage)
                logger.info(f"Reranking completed. Result list size: {len(result_list)}. First item score (if any): {result_list[0].get('score') if result_list else 'N/A'}")

            except Exception as e:
                logger.exception(f"Error during answer reranking: {e}")
                return ChatToFaqFilterResponse(
                    response_code=500,
                    response_text="An error occurred while reranking the answer.",
                    session_id=chat_request.session_id
                )
        else:
            logger.info("Rerank client not available or not initialized. Skipping reranking step.")

        # 5.1 剔除高度重复的答案项，只保留分数最高的一个
        if len(result_list) > 1:
            try:
                # 创建QuickMatcher实例，设置相似度阈值为0.9
                matcher = QuickMatcher(similarity_threshold=0.9)
                
                # 使用deduplicate_by_content方法去重，保留分数最高的项
                # 直接处理result_list，避免model_dump调用
                deduplicated_results = matcher.deduplicate_by_content(
                    items=result_list,
                    content_key="final_answer",
                    score_key="score",
                )
                
                # 更新result_list为去重后的结果
                if deduplicated_results:
                    result_list = deduplicated_results
                    logger.info(f"Deduplication completed: {len(result_list)} results after deduplication(with threshold {matcher.similarity_threshold})")
            except Exception as e:
                logger.warning(f"Failed to deduplicate results: {e}")
                # 出错时保持原始result_list不变

        # 6. 汇总结果
        candidates = []
        # 定义保底答案
        fallback_answer = "<保底话术>未找到具体答案。"
        # 将所有使用量合并到usages中
        usages = ChatModelUsages(models=[usage for usage in model_usages_list if usage])

        result_max_count = 5
        for result in result_list:
            if result['final_answer'] is not None:
                # 有答案
                desc_path_str = self.faq_parser.description_path_to_string(result['desc_path'])
                final_reason = '\n'.join([f"答案路径：{result['key_path']}；路径描述：{desc_path_str}", result['reason']])
                logger.info(f"Finally filtered answer by path({result['key_path']}): {desc_path_str}, answer_size: {len(result['final_answer'])}, score: {result['score']}")
                candidates.append(ChatToFaqFilterCandidate(
                    content=result['final_answer'],
                    category_chain=desc_path_str,
                    score=result['score'],
                    reason=final_reason
                ))

                # 最多返回result_max_count个候选答案
                if len(candidates) >= result_max_count:
                    break
            else:
                # 未找到具体答案
                logger.info("No specific answer found for the query.")
                candidates.append(ChatToFaqFilterCandidate(
                    content=fallback_answer,
                    category_chain="",
                    score=-1.0, 
                    reason=f"答案路径：{result['key_path']}\n无可采纳答案"
                ))

        # 6.1 对结果排序（可选的）
        # final_reason中包含“意图分类”字眼的，整体排在前面，其次按照分数从高到低排列
        candidates.sort(key=lambda x: (x.reason is not None and "意图分类" in x.reason, x.score), reverse=True)

        ### 正式返回完整结果
        return ChatToFaqFilterResponse(
            response_code=200,
            response_body=candidates,
            session_id=chat_request.session_id,
            usages=usages,
            rewritten_query=rewritten_query,
            classify_thinking=classification_thinking,
        )
